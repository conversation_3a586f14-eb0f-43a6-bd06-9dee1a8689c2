package com.investment.debug

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import spock.lang.Shared
import spock.lang.Specification

import java.time.LocalDate

/**
 * Debug test specification to verify that the ADX calculation fix works correctly.
 * This test directly tests the DatabaseManager's DMI calculation to isolate the ADX issue.
 */
class ADXCalculationFixSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/adx_fix_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("**************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        if (databaseManager) {
            databaseManager.closeConnection()
        }
        // Clean up test database
        new File("./data/adx_fix_test.duckdb").delete()
    }

    def "should calculate ADX values correctly without NaN"() {
        given: "a test symbol with sufficient data for DMI calculation"
        String symbol = "ADX_FIX_TEST"
        databaseManager.saveInstrument(symbol, "ADX Fix Test Company", "Technology")
        
        and: "50 days of realistic test data"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI using Pure Java method"
        int recordsUpdated = databaseManager.calculateAndUpdateDMI(symbol, 14, false)
        
        then: "calculation should complete successfully"
        recordsUpdated > 0
        println "Records updated: $recordsUpdated"

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)
        
        then: "should have DMI data"
        def recordsWithDMI = retrievedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
        println "Records with DMI data: ${recordsWithDMI.size()}"

        and: "should have ADX values (this was the bug - ADX was NaN)"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        println "Records with ADX values: ${recordsWithADX.size()}"

        and: "ADX values should be valid numbers (not NaN)"
        recordsWithADX.every { record ->
            !Double.isNaN(record.dmiAdx)
        }
        println "SUCCESS: All ADX values are valid numbers (not NaN)"

        and: "ADX values should be reasonable (between 0 and 100)"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }

        and: "print some sample ADX values for verification"
        recordsWithADX.take(5).each { record ->
            println "Date: ${record.date}, +DI: ${String.format('%.2f', record.dmiPlusDi)}, " +
                   "-DI: ${String.format('%.2f', record.dmiMinusDi)}, " +
                   "DX: ${String.format('%.2f', record.dmiDx)}, " +
                   "ADX: ${String.format('%.2f', record.dmiAdx)}"
        }
    }

    def "should calculate ADX values correctly using Hybrid SQL+Java method"() {
        given: "a test symbol with sufficient data for DMI calculation"
        String symbol = "ADX_HYBRID_TEST"
        databaseManager.saveInstrument(symbol, "ADX Hybrid Test Company", "Technology")
        
        and: "50 days of realistic test data"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI using Hybrid SQL+Java method"
        int recordsUpdated = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)
        
        then: "calculation should complete successfully"
        recordsUpdated > 0
        println "Hybrid method - Records updated: $recordsUpdated"

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)
        
        then: "should have ADX values (this was the bug - ADX was NaN)"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        println "Hybrid method - Records with ADX values: ${recordsWithADX.size()}"

        and: "ADX values should be valid numbers (not NaN)"
        recordsWithADX.every { record ->
            !Double.isNaN(record.dmiAdx)
        }
        println "SUCCESS: Hybrid method - All ADX values are valid numbers (not NaN)"

        and: "ADX values should be reasonable (between 0 and 100)"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }
    }

    /**
     * Create realistic test data with varying price movements to ensure proper DMI calculation.
     */
    private List<OHLCV> createRealisticTestData(String symbol, int days) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(days)
        
        double basePrice = 100.0
        double currentPrice = basePrice
        
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with some volatility
            double change = (Math.random() - 0.5) * 4.0 // +/- 2% change
            currentPrice = Math.max(currentPrice + change, 10.0) // Don't go below $10
            
            double open = currentPrice
            double high = open + Math.random() * 2.0
            double low = open - Math.random() * 2.0
            double close = low + Math.random() * (high - low)
            long volume = (long)(1000000 + Math.random() * 2000000)
            
            // Ensure high >= low and close is between high and low
            if (high < low) {
                double temp = high
                high = low
                low = temp
            }
            close = Math.max(low, Math.min(high, close))
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            currentPrice = close
        }
        
        return data
    }
}
