package com.investment.debug

import com.investment.api.model.DMIRequest
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import com.investment.process.AsyncProcessExecutor
import spock.lang.Specification
import spock.lang.Shared
import com.fasterxml.jackson.databind.ObjectMapper

import java.time.LocalDate

/**
 * Debug test to investigate DMI ADX persistence issue.
 * This test specifically checks if ADX values are being calculated and stored correctly.
 */
class DMIADXPersistenceDebugSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/adx_debug_test.duckdb").delete()

        // Initialize test database
        DatabaseManager.setDbUrl("****************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/adx_debug_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'ADX%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'ADX%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should debug ADX calculation and persistence using DatabaseManager directly"() {
        given: "a test symbol with sufficient data"
        String symbol = "ADX_DEBUG"
        databaseManager.saveInstrument(symbol, "ADX Debug Company", "Technology")

        and: "50 days of realistic test data (more than 28 minimum required)"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        and: "verify data was saved"
        int recordCount = databaseManager.countOhlcvRecords(symbol)
        println "Saved $recordCount records for symbol $symbol"

        when: "performing DMI calculation directly using DatabaseManager"
        println "Executing DMI calculation using DatabaseManager.calculateAndUpdateDMIHybrid"
        int updatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "calculation should complete successfully"
        updatedRecords > 0
        println "DMI calculation completed: $updatedRecords records updated"

        when: "retrieving the calculated data to check ADX values"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol,
                                                                 testData[0].date,
                                                                 testData[-1].date)

        then: "should have retrieved all records"
        retrievedData.size() == testData.size()

        and: "debug ADX values in detail"
        def recordsWithDMI = retrievedData.findAll { it.dmiPlusDi != null }
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        def recordsWithDX = retrievedData.findAll { it.dmiDx != null }

        println "Records with +DI values: ${recordsWithDMI.size()}"
        println "Records with DX values: ${recordsWithDX.size()}"
        println "Records with ADX values: ${recordsWithADX.size()}"

        // Print first few records with DMI data for debugging
        recordsWithDMI.take(10).each { record ->
            println "Date: ${record.date}, +DI: ${record.dmiPlusDi}, -DI: ${record.dmiMinusDi}, DX: ${record.dmiDx}, ADX: ${record.dmiAdx}"
        }

        and: "should have ADX values for records after the initial calculation period"
        recordsWithADX.size() > 0

        and: "ADX values should be reasonable (between 0 and 100)"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }
    }

    def "should test Full Recalculation mode with clearDMIData like the user scenario"() {
        given: "a test symbol with sufficient data"
        String symbol = "AAPL_FULL_RECALC"
        databaseManager.saveInstrument(symbol, "Apple Full Recalc Test", "Technology")

        and: "100 days of realistic test data to ensure sufficient data"
        List<OHLCV> testData = createRealisticTestData(symbol, 100)
        databaseManager.saveOHLCVData(testData)

        and: "verify data was saved"
        int recordCount = databaseManager.countOhlcvRecords(symbol)
        println "Saved $recordCount records for symbol $symbol"

        when: "first clear any existing DMI data (simulating Full Recalc mode)"
        println "Clearing existing DMI data..."
        int clearedRecords = databaseManager.clearDMIData(symbol)
        println "Cleared $clearedRecords records"

        and: "verify data was cleared"
        List<OHLCV> clearedData = databaseManager.getOHLCVData(symbol, testData[0].date, testData[-1].date)
        def recordsWithDMIAfterClear = clearedData.findAll { it.dmiPlusDi != null || it.dmiAdx != null }
        println "Records with DMI data after clear: ${recordsWithDMIAfterClear.size()}"

        then: "no DMI data should remain"
        recordsWithDMIAfterClear.size() == 0

        when: "performing Full Recalculation DMI calculation using Hybrid method"
        println "Executing Full Recalculation DMI calculation using Hybrid method"
        int updatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "calculation should complete successfully"
        updatedRecords > 0
        println "DMI calculation completed: $updatedRecords records updated"

        when: "retrieving the calculated data to check all DMI values including ADX"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, testData[0].date, testData[-1].date)

        then: "should have retrieved all records"
        retrievedData.size() == testData.size()

        and: "debug all DMI values in detail"
        def recordsWithPlusDI = retrievedData.findAll { it.dmiPlusDi != null }
        def recordsWithMinusDI = retrievedData.findAll { it.dmiMinusDi != null }
        def recordsWithDX = retrievedData.findAll { it.dmiDx != null }
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }

        println "Records with +DI values: ${recordsWithPlusDI.size()}"
        println "Records with -DI values: ${recordsWithMinusDI.size()}"
        println "Records with DX values: ${recordsWithDX.size()}"
        println "Records with ADX values: ${recordsWithADX.size()}"

        // Print detailed breakdown of DMI data
        println "\nDetailed DMI data breakdown:"
        retrievedData.eachWithIndex { record, index ->
            if (record.dmiPlusDi != null || record.dmiAdx != null) {
                println "[$index] Date: ${record.date}, +DI: ${record.dmiPlusDi}, -DI: ${record.dmiMinusDi}, DX: ${record.dmiDx}, ADX: ${record.dmiAdx}"
            }
        }

        and: "should have ADX values for records after the initial calculation period"
        recordsWithADX.size() > 0
        println "SUCCESS: Found ${recordsWithADX.size()} records with ADX values"

        and: "ADX values should be reasonable (between 0 and 100)"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }

        and: "ADX should start appearing after sufficient data points (around day 28)"
        def firstADXRecord = recordsWithADX.first()
        def firstADXIndex = retrievedData.indexOf(firstADXRecord)
        println "First ADX value appears at index $firstADXIndex (should be around 27 or later)"
        firstADXIndex >= 27 // ADX needs 2*period (28) days minimum
    }

    def "should test with real AAPL data if available"() {
        given: "check if AAPL data exists in the database"
        boolean aaplExists = false
        int aaplRecordCount = 0
        try {
            aaplRecordCount = databaseManager.countOhlcvRecords("AAPL")
            aaplExists = aaplRecordCount > 50 // Need sufficient data
            println "AAPL exists: $aaplExists, Record count: $aaplRecordCount"
        } catch (Exception e) {
            println "AAPL not found or error: ${e.message}"
        }

        when: "AAPL data exists and we perform Full Recalculation"
        if (aaplExists) {
            println "Testing with real AAPL data ($aaplRecordCount records)"

            // Clear existing DMI data
            int clearedRecords = databaseManager.clearDMIData("AAPL")
            println "Cleared $clearedRecords AAPL DMI records"

            // Perform Full Recalculation
            int updatedRecords = databaseManager.calculateAndUpdateDMIHybrid("AAPL", 14, false)
            println "Updated $updatedRecords AAPL records with DMI data"

            // Get recent data to check ADX values
            LocalDate endDate = LocalDate.now()
            LocalDate startDate = endDate.minusDays(100)
            List<OHLCV> aaplData = databaseManager.getOHLCVData("AAPL", startDate, endDate)

            def recordsWithADX = aaplData.findAll { it.dmiAdx != null }
            println "AAPL records with ADX values: ${recordsWithADX.size()}"

            // Print some ADX values for debugging
            recordsWithADX.take(5).each { record ->
                println "AAPL Date: ${record.date}, ADX: ${record.dmiAdx}"
            }

            if (recordsWithADX.size() == 0) {
                println "ERROR: No ADX values found for AAPL after Full Recalculation!"

                // Debug: Check if other DMI values exist
                def recordsWithDMI = aaplData.findAll { it.dmiPlusDi != null }
                println "AAPL records with +DI values: ${recordsWithDMI.size()}"

                recordsWithDMI.take(5).each { record ->
                    println "AAPL DMI Date: ${record.date}, +DI: ${record.dmiPlusDi}, -DI: ${record.dmiMinusDi}, DX: ${record.dmiDx}, ADX: ${record.dmiAdx}"
                }
            }
        } else {
            println "Skipping AAPL test - insufficient data or AAPL not found"
        }

        then: "if AAPL data exists, ADX should be calculated"
        if (aaplExists) {
            // This is where we might find the actual issue
            true // For now, just ensure the test runs
        } else {
            true // Skip if no AAPL data
        }
    }

    def "should test JSON deserialization of calculationMode"() {
        given: "a JSON string representing the frontend request"
        String frontendJson = '''
        {
            "calculationMode": "FULL_RECALCULATION",
            "period": 14,
            "minDataPoints": 28,
            "dryRun": false
        }
        '''

        when: "deserializing to DMIRequest"
        ObjectMapper mapper = new ObjectMapper()
        DMIRequest request = mapper.readValue(frontendJson, DMIRequest.class)

        then: "calculationMode should be correctly set"
        request.calculationMode == DMIRequest.CalculationMode.FULL_RECALCULATION
        request.period == 14
        request.minDataPoints == 28
        request.dryRun == false

        println "Deserialized DMIRequest: $request"
        println "CalculationMode: ${request.calculationMode}"
    }

    def "should test JSON deserialization with wrong field name fails appropriately"() {
        given: "a JSON string with the old field name 'mode'"
        String frontendJsonWithWrongField = '''
        {
            "mode": "FULL_RECALCULATION",
            "period": 14,
            "minDataPoints": 28,
            "dryRun": false
        }
        '''

        when: "deserializing to DMIRequest"
        ObjectMapper mapper = new ObjectMapper()
        DMIRequest request = mapper.readValue(frontendJsonWithWrongField, DMIRequest.class)

        then: "should throw UnrecognizedPropertyException"
        thrown(com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException)

        println "Good! Jackson correctly rejects unknown 'mode' field"
    }

    def "should verify the fix works end-to-end"() {
        given: "a test symbol with sufficient data"
        String symbol = "FULL_RECALC_TEST"
        databaseManager.saveInstrument(symbol, "Full Recalc Test Company", "Technology")

        and: "50 days of realistic test data"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        and: "a properly formatted DMI request with FULL_RECALCULATION mode"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        request.symbols = [symbol]
        request.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA

        when: "processing the request through the service layer"
        println "Testing FULL_RECALCULATION mode end-to-end"
        println "Request: $request"

        // Create a mock service to test (since we don't have the full Spring context)
        DMIService testService = new DMIService(databaseManager, Mock(AsyncProcessExecutor))
        def response = testService.calculateDMI(request)

        then: "calculation should complete successfully"
        response.status == "success"
        response.processedSymbols == 1
        response.totalRecordsUpdated > 0
        response.parameters.calculationMode == DMIRequest.CalculationMode.FULL_RECALCULATION

        println "Response: ${response.summary}"
        println "Calculation mode in response: ${response.parameters.calculationMode}"

        when: "retrieving the calculated data to verify ADX values"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, testData[0].date, testData[-1].date)
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }

        then: "should have ADX values"
        recordsWithADX.size() > 0
        println "SUCCESS: Found ${recordsWithADX.size()} records with ADX values after FULL_RECALCULATION"

        // Print some ADX values for verification
        recordsWithADX.take(3).each { record ->
            println "Date: ${record.date}, ADX: ${record.dmiAdx}"
        }

        and: "ADX values should be reasonable"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }
    }

    def "should debug ADX calculation with Pure Java method"() {
        given: "a test symbol with sufficient data"
        String symbol = "ADX_JAVA_DEBUG"
        databaseManager.saveInstrument(symbol, "ADX Java Debug Company", "Technology")
        
        and: "50 days of realistic test data"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "performing Full Recalculation with Pure Java method"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        request.symbols = [symbol]
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        
        println "Executing Pure Java DMI calculation"
        def response = dmiService.calculateDMI(request)
        
        then: "calculation should complete successfully"
        response.status == "success"
        response.processedSymbols == 1
        response.totalRecordsUpdated > 0

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)
        
        then: "should have ADX values"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        println "Pure Java - Records with ADX values: ${recordsWithADX.size()}"
        
        recordsWithADX.size() > 0
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }
    }

    def "should compare Hybrid vs Pure Java ADX calculations"() {
        given: "two identical test symbols"
        String hybridSymbol = "ADX_HYBRID_CMP"
        String javaSymbol = "ADX_JAVA_CMP"
        
        databaseManager.saveInstrument(hybridSymbol, "Hybrid Comparison", "Technology")
        databaseManager.saveInstrument(javaSymbol, "Java Comparison", "Technology")
        
        and: "identical test data for both"
        List<OHLCV> testData = createRealisticTestData("BASE", 50)
        List<OHLCV> hybridData = testData.collect { 
            new OHLCV(hybridSymbol, it.date, it.open, it.high, it.low, it.close, it.volume)
        }
        List<OHLCV> javaData = testData.collect { 
            new OHLCV(javaSymbol, it.date, it.open, it.high, it.low, it.close, it.volume)
        }
        
        databaseManager.saveOHLCVData(hybridData)
        databaseManager.saveOHLCVData(javaData)

        when: "calculating with both methods"
        DMIRequest hybridRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        hybridRequest.symbols = [hybridSymbol]
        hybridRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        
        DMIRequest javaRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        javaRequest.symbols = [javaSymbol]
        javaRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        
        def hybridResponse = dmiService.calculateDMI(hybridRequest)
        def javaResponse = dmiService.calculateDMI(javaRequest)
        
        then: "both should succeed"
        hybridResponse.status == "success"
        javaResponse.status == "success"

        when: "comparing results"
        List<OHLCV> hybridResults = databaseManager.getOHLCVData(hybridSymbol, testData[0].date, testData[-1].date)
        List<OHLCV> javaResults = databaseManager.getOHLCVData(javaSymbol, testData[0].date, testData[-1].date)
        
        def hybridADX = hybridResults.findAll { it.dmiAdx != null }
        def javaADX = javaResults.findAll { it.dmiAdx != null }
        
        then: "both should have ADX values"
        println "Hybrid ADX records: ${hybridADX.size()}, Java ADX records: ${javaADX.size()}"
        
        hybridADX.size() > 0
        javaADX.size() > 0
        hybridADX.size() == javaADX.size()
        
        and: "ADX values should be similar (within reasonable tolerance)"
        for (int i = 0; i < Math.min(hybridADX.size(), javaADX.size()); i++) {
            def hybridVal = hybridADX[i].dmiAdx
            def javaVal = javaADX[i].dmiAdx
            def diff = Math.abs(hybridVal - javaVal)
            println "Date: ${hybridADX[i].date}, Hybrid ADX: $hybridVal, Java ADX: $javaVal, Diff: $diff"
            
            // Allow for small numerical differences between methods
            assert diff < 1.0 : "ADX values differ too much: Hybrid=$hybridVal, Java=$javaVal"
        }
    }

    /**
     * Create realistic test data with varying price movements to ensure proper DMI calculation.
     */
    private List<OHLCV> createRealisticTestData(String symbol, int days) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(days)
        double basePrice = 100.0
        Random random = new Random(42) // Fixed seed for reproducible results
        
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with some volatility
            double change = (random.nextGaussian() * 0.02) // 2% daily volatility
            basePrice = Math.max(basePrice * (1 + change), 10.0) // Prevent negative prices
            
            double open = basePrice
            double high = open * (1 + Math.abs(random.nextGaussian() * 0.01))
            double low = open * (1 - Math.abs(random.nextGaussian() * 0.01))
            double close = low + (high - low) * random.nextDouble()
            long volume = (long) (1000000 + random.nextInt(500000))
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close // Use close as next day's base
        }
        
        return data
    }
}
